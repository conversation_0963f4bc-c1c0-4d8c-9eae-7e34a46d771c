#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مبسط للكاميرا مع Flask
"""

from flask import Flask, Response, render_template_string, jsonify, request
import cv2
import threading
import time

app = Flask(__name__)

class SimpleCameraSystem:
    def __init__(self):
        self.camera = None
        self.is_running = False

    def start_camera(self, camera_id=0):
        """بدء تشغيل الكاميرا"""
        try:
            print(f"🎥 محاولة فتح الكاميرا {camera_id}")

            # إيقاف الكاميرا الحالية إذا كانت تعمل
            if self.camera:
                self.camera.release()
                self.camera = None

            # فتح الكاميرا
            self.camera = cv2.VideoCapture(camera_id, cv2.CAP_DSHOW)

            if not self.camera.isOpened():
                print(f"❌ فشل في فتح الكاميرا {camera_id}")
                return False, f"فشل في فتح الكاميرا {camera_id}"

            # تعيين إعدادات
            self.camera.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            self.camera.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)

            # اختبار قراءة إطار
            ret, test_frame = self.camera.read()
            if not ret or test_frame is None:
                print("❌ الكاميرا متصلة لكن لا تقرأ إطارات")
                self.camera.release()
                self.camera = None
                return False, "الكاميرا متصلة لكن لا تقرأ إطارات"

            self.is_running = True
            print(f"✅ تم تشغيل الكاميرا {camera_id} بنجاح")
            return True, f"تم تشغيل الكاميرا {camera_id} بنجاح"

        except Exception as e:
            print(f"❌ خطأ في تشغيل الكاميرا: {e}")
            if self.camera:
                self.camera.release()
                self.camera = None
            return False, f"خطأ: {str(e)}"

    def stop_camera(self):
        """إيقاف الكاميرا"""
        print("🛑 إيقاف الكاميرا")
        self.is_running = False
        if self.camera:
            self.camera.release()
            self.camera = None

    def get_frame(self):
        """الحصول على إطار من الكاميرا"""
        if not self.camera or not self.is_running:
            return None

        ret, frame = self.camera.read()
        if not ret:
            print("⚠️ فشل في قراءة إطار من الكاميرا")
            return None

        # رسم نص للتأكد من عمل الكاميرا
        cv2.putText(frame, f'Camera Working - {time.strftime("%H:%M:%S")}',
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)

        return frame

# إنشاء نظام الكاميرا
camera_system = SimpleCameraSystem()

# HTML مبسط
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الكاميرا</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .controls { margin: 20px 0; }
        button { padding: 10px 20px; margin: 5px; font-size: 16px; }
        .success { background: #4CAF50; color: white; }
        .error { background: #f44336; color: white; }
        #video { max-width: 100%; border: 2px solid #ddd; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 اختبار الكاميرا المبسط</h1>

        <div class="controls">
            <button onclick="startCamera()" id="startBtn">تشغيل الكاميرا</button>
            <button onclick="stopCamera()" id="stopBtn">إيقاف الكاميرا</button>
            <button onclick="testCamera()">اختبار الكاميرا</button>
        </div>

        <div id="status"></div>

        <div>
            <img id="video" src="" alt="عرض الكاميرا" style="display: none;">
            <div id="placeholder" style="text-align: center; padding: 50px; background: #f0f0f0;">
                اضغط "تشغيل الكاميرا" لبدء العرض
            </div>
        </div>
    </div>

    <script>
        function showStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.innerHTML = message;
            status.className = 'status ' + type;
            console.log(type.toUpperCase() + ':', message);
        }

        async function startCamera() {
            showStatus('جاري تشغيل الكاميرا...', 'info');

            try {
                const response = await fetch('/start_camera', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ camera_id: 0 })
                });

                const result = await response.json();

                if (result.success) {
                    showStatus(result.message, 'success');
                    startVideoStream();
                } else {
                    showStatus(result.message, 'error');
                }
            } catch (error) {
                showStatus('خطأ في الاتصال: ' + error.message, 'error');
            }
        }

        async function stopCamera() {
            try {
                const response = await fetch('/stop_camera', { method: 'POST' });
                const result = await response.json();

                showStatus(result.message, 'success');
                stopVideoStream();
            } catch (error) {
                showStatus('خطأ في إيقاف الكاميرا: ' + error.message, 'error');
            }
        }

        function startVideoStream() {
            const video = document.getElementById('video');
            const placeholder = document.getElementById('placeholder');

            video.src = '/video_feed?' + new Date().getTime();
            video.style.display = 'block';
            placeholder.style.display = 'none';

            video.onerror = () => {
                showStatus('خطأ في تدفق الفيديو', 'error');
            };
        }

        function stopVideoStream() {
            const video = document.getElementById('video');
            const placeholder = document.getElementById('placeholder');

            video.src = '';
            video.style.display = 'none';
            placeholder.style.display = 'block';
        }

        async function testCamera() {
            try {
                const response = await fetch('/test_camera');
                const result = await response.json();

                if (result.success) {
                    showStatus('✅ ' + result.message, 'success');
                } else {
                    showStatus('❌ ' + result.message, 'error');
                }
            } catch (error) {
                showStatus('خطأ في اختبار الكاميرا: ' + error.message, 'error');
            }
        }
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    return render_template_string(HTML_TEMPLATE)

@app.route('/start_camera', methods=['POST'])
def start_camera():
    data = request.get_json()
    camera_id = data.get('camera_id', 0)

    print(f"📥 طلب تشغيل الكاميرا {camera_id}")
    success, message = camera_system.start_camera(camera_id)

    return jsonify({'success': success, 'message': message})

@app.route('/stop_camera', methods=['POST'])
def stop_camera():
    print("📥 طلب إيقاف الكاميرا")
    camera_system.stop_camera()
    return jsonify({'success': True, 'message': 'تم إيقاف الكاميرا'})

@app.route('/test_camera')
def test_camera():
    """اختبار سريع للكاميرا"""
    try:
        cap = cv2.VideoCapture(0, cv2.CAP_DSHOW)
        if not cap.isOpened():
            return jsonify({'success': False, 'message': 'فشل في فتح الكاميرا'})

        ret, frame = cap.read()
        cap.release()

        if not ret:
            return jsonify({'success': False, 'message': 'الكاميرا متصلة لكن لا تقرأ إطارات'})

        return jsonify({'success': True, 'message': f'الكاميرا تعمل - حجم الإطار: {frame.shape}'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ: {str(e)}'})

@app.route('/video_feed')
def video_feed():
    """تدفق الفيديو"""
    def generate():
        print("📹 بدء تدفق الفيديو...")

        while camera_system.is_running:
            frame = camera_system.get_frame()
            if frame is None:
                print("⚠️ لا يوجد إطار متاح")
                break

            ret, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 80])
            if not ret:
                print("❌ فشل في تحويل الإطار")
                continue

            frame_bytes = buffer.tobytes()
            yield (b'--frame\r\n'
                   b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')

        print("🛑 انتهى تدفق الفيديو")

    return Response(generate(), mimetype='multipart/x-mixed-replace; boundary=frame')

if __name__ == '__main__':
    import sys
    print("🧪 تشغيل اختبار الكاميرا المبسط...", flush=True)
    print("📍 افتح المتصفح على: http://localhost:5001", flush=True)
    print("-" * 50, flush=True)

    # تأكد من طباعة الرسائل فوراً
    sys.stdout.flush()

    try:
        app.run(debug=True, host='0.0.0.0', port=5001, use_reloader=False)
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}", flush=True)
