from flask import Flask, render_template, jsonify
import cv2

app = Flask(__name__)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/test_camera')
def test_camera():
    """اختبار الكاميرا"""
    try:
        # محاولة فتح الكاميرا
        cap = cv2.VideoCapture(0, cv2.CAP_DSHOW)
        
        if not cap.isOpened():
            return jsonify({
                'success': False, 
                'message': 'فشل في فتح الكاميرا 0'
            })
        
        # محاولة قراءة إطار
        ret, frame = cap.read()
        cap.release()
        
        if not ret:
            return jsonify({
                'success': False, 
                'message': 'الكاميرا متصلة لكن لا يمكن قراءة الإطارات'
            })
        
        return jsonify({
            'success': True, 
            'message': f'الكاميرا تعمل بنجاح - حجم الإطار: {frame.shape}'
        })
        
    except Exception as e:
        return jsonify({
            'success': False, 
            'message': f'خطأ: {str(e)}'
        })

@app.route('/get_cameras')
def get_cameras():
    """الحصول على قائمة الكاميرات المتاحة"""
    cameras = []
    
    for i in range(5):
        try:
            cap = cv2.VideoCapture(i, cv2.CAP_DSHOW)
            if cap.isOpened():
                ret, _ = cap.read()
                if ret:
                    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                    cameras.append({
                        'id': i,
                        'name': f'كاميرا {i} ({width}x{height})',
                        'working': True
                    })
                else:
                    cameras.append({
                        'id': i,
                        'name': f'كاميرا {i} (غير متاحة)',
                        'working': False
                    })
            cap.release()
        except Exception as e:
            cameras.append({
                'id': i,
                'name': f'كاميرا {i} (خطأ: {str(e)})',
                'working': False
            })
    
    return jsonify(cameras)

if __name__ == '__main__':
    print("🧪 تشغيل اختبار الكاميرا...")
    print("📍 افتح المتصفح على: http://localhost:5000")
    print("🔍 اختبر الكاميرا على: http://localhost:5000/test_camera")
    print("📋 قائمة الكاميرات: http://localhost:5000/get_cameras")
    print("-" * 50)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
