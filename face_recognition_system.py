import cv2
import face_recognition
import numpy as np
import os
from database import FaceDatabase
import threading
import time
from datetime import datetime

class FaceRecognitionSystem:
    def __init__(self):
        self.db = FaceDatabase()
        self.known_face_encodings = []
        self.known_face_names = []
        self.camera = None
        self.is_running = False
        self.recognition_threshold = 0.6
        self.load_known_faces()
        
    def load_known_faces(self):
        """تحميل الوجوه المعروفة من قاعدة البيانات"""
        faces = self.db.get_known_faces()
        self.known_face_encodings = []
        self.known_face_names = []
        
        for face in faces:
            self.known_face_encodings.append(np.array(face['encoding']))
            self.known_face_names.append(face['name'])
    
    def add_face_from_image(self, image_path, person_name):
        """إضافة وجه جديد من صورة"""
        try:
            # تحميل الصورة
            image = face_recognition.load_image_file(image_path)
            
            # العثور على الوجوه في الصورة
            face_locations = face_recognition.face_locations(image)
            
            if len(face_locations) == 0:
                return False, "لم يتم العثور على وجه في الصورة"
            
            if len(face_locations) > 1:
                return False, "تم العثور على أكثر من وجه في الصورة. يرجى استخدام صورة تحتوي على وجه واحد فقط"
            
            # الحصول على ترميز الوجه
            face_encoding = face_recognition.face_encodings(image, face_locations)[0]
            
            # حفظ في قاعدة البيانات
            face_id = self.db.add_known_face(person_name, face_encoding, image_path)
            
            # إعادة تحميل الوجوه المعروفة
            self.load_known_faces()
            
            return True, f"تم إضافة الوجه بنجاح. ID: {face_id}"
            
        except Exception as e:
            return False, f"خطأ في إضافة الوجه: {str(e)}"
    
    def get_available_cameras(self):
        """الحصول على قائمة الكاميرات المتاحة"""
        cameras = []
        
        # فحص الكاميرات المحلية (0-10)
        for i in range(10):
            cap = cv2.VideoCapture(i)
            if cap.isOpened():
                ret, frame = cap.read()
                if ret:
                    cameras.append({
                        'id': i,
                        'name': f'كاميرا محلية {i}',
                        'type': 'local'
                    })
                cap.release()
        
        return cameras
    
    def start_camera(self, camera_id=0, camera_url=None):
        """بدء تشغيل الكاميرا"""
        try:
            if camera_url:
                self.camera = cv2.VideoCapture(camera_url)
            else:
                self.camera = cv2.VideoCapture(camera_id)
            
            if not self.camera.isOpened():
                return False, "فشل في فتح الكاميرا"
            
            # تعيين دقة الكاميرا
            self.camera.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            self.camera.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            
            return True, "تم تشغيل الكاميرا بنجاح"
            
        except Exception as e:
            return False, f"خطأ في تشغيل الكاميرا: {str(e)}"
    
    def stop_camera(self):
        """إيقاف الكاميرا"""
        if self.camera:
            self.camera.release()
            self.camera = None
        self.is_running = False
    
    def recognize_faces_in_frame(self, frame):
        """التعرف على الوجوه في إطار واحد"""
        # تصغير الإطار لتسريع المعالجة
        small_frame = cv2.resize(frame, (0, 0), fx=0.25, fy=0.25)
        rgb_small_frame = small_frame[:, :, ::-1]
        
        # العثور على الوجوه
        face_locations = face_recognition.face_locations(rgb_small_frame)
        face_encodings = face_recognition.face_encodings(rgb_small_frame, face_locations)
        
        face_names = []
        face_confidences = []
        
        for face_encoding in face_encodings:
            # مقارنة مع الوجوه المعروفة
            matches = face_recognition.compare_faces(self.known_face_encodings, face_encoding)
            name = "غير معروف"
            confidence = 0
            
            # استخدام المسافة للحصول على أفضل تطابق
            face_distances = face_recognition.face_distance(self.known_face_encodings, face_encoding)
            
            if len(face_distances) > 0:
                best_match_index = np.argmin(face_distances)
                if matches[best_match_index] and face_distances[best_match_index] < self.recognition_threshold:
                    name = self.known_face_names[best_match_index]
                    confidence = 1 - face_distances[best_match_index]
                    
                    # تسجيل التعرف
                    self.db.log_recognition(name, confidence)
            
            face_names.append(name)
            face_confidences.append(confidence)
        
        # تكبير مواقع الوجوه للإطار الأصلي
        face_locations = [(top*4, right*4, bottom*4, left*4) for (top, right, bottom, left) in face_locations]
        
        return face_locations, face_names, face_confidences
    
    def draw_face_boxes(self, frame, face_locations, face_names, face_confidences):
        """رسم مربعات حول الوجوه مع الأسماء"""
        for (top, right, bottom, left), name, confidence in zip(face_locations, face_names, face_confidences):
            # اختيار لون المربع
            if name != "غير معروف":
                color = (0, 255, 0)  # أخضر للوجوه المعروفة
            else:
                color = (0, 0, 255)  # أحمر للوجوه غير المعروفة
            
            # رسم المربع
            cv2.rectangle(frame, (left, top), (right, bottom), color, 2)
            
            # رسم خلفية للنص
            cv2.rectangle(frame, (left, bottom - 35), (right, bottom), color, cv2.FILLED)
            
            # كتابة الاسم والثقة
            font = cv2.FONT_HERSHEY_DUPLEX
            text = f"{name}"
            if confidence > 0:
                text += f" ({confidence:.2f})"
            
            cv2.putText(frame, text, (left + 6, bottom - 6), font, 0.6, (255, 255, 255), 1)
        
        return frame
    
    def get_frame(self):
        """الحصول على إطار واحد من الكاميرا مع التعرف على الوجوه"""
        if not self.camera:
            return None
        
        ret, frame = self.camera.read()
        if not ret:
            return None
        
        # التعرف على الوجوه
        face_locations, face_names, face_confidences = self.recognize_faces_in_frame(frame)
        
        # رسم المربعات والأسماء
        frame = self.draw_face_boxes(frame, face_locations, face_names, face_confidences)
        
        return frame
    
    def set_recognition_threshold(self, threshold):
        """تعيين عتبة التعرف"""
        self.recognition_threshold = max(0.1, min(1.0, threshold))
        self.db.set_setting('recognition_threshold', str(self.recognition_threshold))
