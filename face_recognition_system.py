import cv2
import numpy as np
import os
from database import FaceDatabase
import threading
import time
from datetime import datetime

# محاولة استيراد face_recognition، إذا لم تكن متوفرة نستخدم نسخة مبسطة
try:
    import face_recognition
    FACE_RECOGNITION_AVAILABLE = True
except ImportError:
    FACE_RECOGNITION_AVAILABLE = False
    print("تحذير: مكتبة face_recognition غير متوفرة. سيتم تشغيل النظام بدون التعرف على الوجوه.")

class FaceRecognitionSystem:
    def __init__(self):
        self.db = FaceDatabase()
        self.known_face_encodings = []
        self.known_face_names = []
        self.camera = None
        self.is_running = False
        self.recognition_threshold = 0.6
        self.load_known_faces()

    def load_known_faces(self):
        """تحميل الوجوه المعروفة من قاعدة البيانات"""
        faces = self.db.get_known_faces()
        self.known_face_encodings = []
        self.known_face_names = []

        for face in faces:
            self.known_face_encodings.append(np.array(face['encoding']))
            self.known_face_names.append(face['name'])

    def add_face_from_image(self, image_path, person_name):
        """إضافة وجه جديد من صورة"""
        if not FACE_RECOGNITION_AVAILABLE:
            return False, "مكتبة التعرف على الوجوه غير متوفرة. يرجى تثبيت face_recognition أولاً."

        try:
            # تحميل الصورة
            image = face_recognition.load_image_file(image_path)

            # العثور على الوجوه في الصورة
            face_locations = face_recognition.face_locations(image)

            if len(face_locations) == 0:
                return False, "لم يتم العثور على وجه في الصورة"

            if len(face_locations) > 1:
                return False, "تم العثور على أكثر من وجه في الصورة. يرجى استخدام صورة تحتوي على وجه واحد فقط"

            # الحصول على ترميز الوجه
            face_encoding = face_recognition.face_encodings(image, face_locations)[0]

            # حفظ في قاعدة البيانات
            face_id = self.db.add_known_face(person_name, face_encoding, image_path)

            # إعادة تحميل الوجوه المعروفة
            self.load_known_faces()

            return True, f"تم إضافة الوجه بنجاح. ID: {face_id}"

        except Exception as e:
            return False, f"خطأ في إضافة الوجه: {str(e)}"

    def get_available_cameras(self):
        """الحصول على قائمة الكاميرات المتاحة"""
        cameras = []

        # فحص الكاميرات المحلية (0-5)
        for i in range(5):
            try:
                cap = cv2.VideoCapture(i, cv2.CAP_DSHOW)  # استخدام DirectShow على Windows
                if cap.isOpened():
                    # محاولة قراءة إطار للتأكد من عمل الكاميرا
                    ret, _ = cap.read()
                    if ret:
                        # الحصول على معلومات الكاميرا
                        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                        fps = int(cap.get(cv2.CAP_PROP_FPS))

                        cameras.append({
                            'id': i,
                            'name': f'كاميرا محلية {i} ({width}x{height}@{fps}fps)',
                            'type': 'local',
                            'width': width,
                            'height': height,
                            'fps': fps
                        })
                cap.release()
            except Exception as e:
                print(f"خطأ في فحص الكاميرا {i}: {e}")
                continue

        # إضافة خيار كاميرا افتراضية إذا لم يتم العثور على أي كاميرا
        if not cameras:
            cameras.append({
                'id': 0,
                'name': 'كاميرا افتراضية (0)',
                'type': 'default'
            })

        return cameras

    def start_camera(self, camera_id=0, camera_url=None):
        """بدء تشغيل الكاميرا"""
        try:
            # إيقاف الكاميرا الحالية إذا كانت تعمل
            if self.camera:
                self.camera.release()
                self.camera = None

            if camera_url:
                print(f"محاولة الاتصال بكاميرا IP: {camera_url}")
                self.camera = cv2.VideoCapture(camera_url)
            else:
                print(f"محاولة فتح الكاميرا المحلية: {camera_id}")
                # استخدام DirectShow على Windows لتحسين الأداء
                self.camera = cv2.VideoCapture(camera_id, cv2.CAP_DSHOW)

            if not self.camera.isOpened():
                return False, f"فشل في فتح الكاميرا {camera_id if not camera_url else camera_url}"

            # تعيين إعدادات الكاميرا
            self.camera.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            self.camera.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            self.camera.set(cv2.CAP_PROP_FPS, 30)

            # اختبار قراءة إطار للتأكد من عمل الكاميرا
            ret, test_frame = self.camera.read()
            if not ret or test_frame is None:
                self.camera.release()
                self.camera = None
                return False, "الكاميرا متصلة لكن لا يمكن قراءة الإطارات منها"

            # الحصول على الدقة الفعلية
            actual_width = int(self.camera.get(cv2.CAP_PROP_FRAME_WIDTH))
            actual_height = int(self.camera.get(cv2.CAP_PROP_FRAME_HEIGHT))
            actual_fps = int(self.camera.get(cv2.CAP_PROP_FPS))

            print(f"تم تشغيل الكاميرا بنجاح - الدقة: {actual_width}x{actual_height}@{actual_fps}fps")
            return True, f"تم تشغيل الكاميرا بنجاح ({actual_width}x{actual_height})"

        except Exception as e:
            if self.camera:
                self.camera.release()
                self.camera = None
            return False, f"خطأ في تشغيل الكاميرا: {str(e)}"

    def stop_camera(self):
        """إيقاف الكاميرا"""
        if self.camera:
            self.camera.release()
            self.camera = None
        self.is_running = False

    def recognize_faces_in_frame(self, frame):
        """التعرف على الوجوه في إطار واحد"""
        if not FACE_RECOGNITION_AVAILABLE:
            # استخدام OpenCV للكشف عن الوجوه فقط (بدون التعرف)
            return self.detect_faces_opencv(frame)

        try:
            # تصغير الإطار لتسريع المعالجة
            small_frame = cv2.resize(frame, (0, 0), fx=0.25, fy=0.25)
            rgb_small_frame = small_frame[:, :, ::-1]

            # العثور على الوجوه
            face_locations = face_recognition.face_locations(rgb_small_frame)
            face_encodings = face_recognition.face_encodings(rgb_small_frame, face_locations)

            face_names = []
            face_confidences = []

            for face_encoding in face_encodings:
                # مقارنة مع الوجوه المعروفة
                matches = face_recognition.compare_faces(self.known_face_encodings, face_encoding)
                name = "غير معروف"
                confidence = 0

                # استخدام المسافة للحصول على أفضل تطابق
                face_distances = face_recognition.face_distance(self.known_face_encodings, face_encoding)

                if len(face_distances) > 0:
                    best_match_index = np.argmin(face_distances)
                    if matches[best_match_index] and face_distances[best_match_index] < self.recognition_threshold:
                        name = self.known_face_names[best_match_index]
                        confidence = 1 - face_distances[best_match_index]

                        # تسجيل التعرف
                        self.db.log_recognition(name, confidence)

                face_names.append(name)
                face_confidences.append(confidence)

            # تكبير مواقع الوجوه للإطار الأصلي
            face_locations = [(top*4, right*4, bottom*4, left*4) for (top, right, bottom, left) in face_locations]

            return face_locations, face_names, face_confidences
        except Exception as e:
            print(f"خطأ في التعرف على الوجوه: {e}")
            return [], [], []

    def detect_faces_opencv(self, frame):
        """كشف الوجوه باستخدام OpenCV فقط (بدون التعرف)"""
        try:
            # تحويل إلى رمادي
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

            # تحميل مصنف الوجوه
            face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')

            # كشف الوجوه
            faces = face_cascade.detectMultiScale(gray, 1.1, 4)

            face_locations = []
            face_names = []
            face_confidences = []

            for (x, y, w, h) in faces:
                # تحويل إلى تنسيق face_recognition (top, right, bottom, left)
                face_locations.append((y, x + w, y + h, x))
                face_names.append("وجه مكتشف")
                face_confidences.append(0.8)  # ثقة افتراضية

            return face_locations, face_names, face_confidences
        except Exception as e:
            print(f"خطأ في كشف الوجوه: {e}")
            return [], [], []

    def draw_face_boxes(self, frame, face_locations, face_names, face_confidences):
        """رسم مربعات حول الوجوه مع الأسماء"""
        for (top, right, bottom, left), name, confidence in zip(face_locations, face_names, face_confidences):
            # اختيار لون المربع
            if name != "غير معروف":
                color = (0, 255, 0)  # أخضر للوجوه المعروفة
            else:
                color = (0, 0, 255)  # أحمر للوجوه غير المعروفة

            # رسم المربع
            cv2.rectangle(frame, (left, top), (right, bottom), color, 2)

            # رسم خلفية للنص
            cv2.rectangle(frame, (left, bottom - 35), (right, bottom), color, cv2.FILLED)

            # كتابة الاسم والثقة
            font = cv2.FONT_HERSHEY_DUPLEX
            text = f"{name}"
            if confidence > 0:
                text += f" ({confidence:.2f})"

            cv2.putText(frame, text, (left + 6, bottom - 6), font, 0.6, (255, 255, 255), 1)

        return frame

    def get_frame(self):
        """الحصول على إطار واحد من الكاميرا مع التعرف على الوجوه"""
        if not self.camera:
            return None

        ret, frame = self.camera.read()
        if not ret:
            return None

        # التعرف على الوجوه
        face_locations, face_names, face_confidences = self.recognize_faces_in_frame(frame)

        # رسم المربعات والأسماء
        frame = self.draw_face_boxes(frame, face_locations, face_names, face_confidences)

        return frame

    def set_recognition_threshold(self, threshold):
        """تعيين عتبة التعرف"""
        self.recognition_threshold = max(0.1, min(1.0, threshold))
        self.db.set_setting('recognition_threshold', str(self.recognition_threshold))
