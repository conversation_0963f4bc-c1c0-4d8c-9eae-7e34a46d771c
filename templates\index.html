<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام التعرف على الوجوه</title>

    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-eye me-2"></i>
                نظام التعرف على الوجوه
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#camera-section">الكاميرا</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#faces-section">الوجوه المعروفة</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#logs-section">السجلات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#settings-section">الإعدادات</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Camera Section -->
            <div class="col-lg-8" id="camera-section">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-video me-2"></i>
                            عرض الكاميرا المباشر
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Camera Controls -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="cameraSelect" class="form-label">اختيار الكاميرا:</label>
                                <select class="form-select" id="cameraSelect">
                                    <option value="">جاري البحث عن الكاميرات...</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="cameraUrl" class="form-label">رابط كاميرا IP (اختياري):</label>
                                <input type="url" class="form-control" id="cameraUrl" placeholder="http://*************:8080/video">
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-12">
                                <button class="btn btn-success me-2" id="startCameraBtn">
                                    <i class="fas fa-play me-1"></i>
                                    تشغيل الكاميرا
                                </button>
                                <button class="btn btn-danger me-2" id="stopCameraBtn" disabled>
                                    <i class="fas fa-stop me-1"></i>
                                    إيقاف الكاميرا
                                </button>
                                <button class="btn btn-info me-2" id="captureBtn" disabled>
                                    <i class="fas fa-camera me-1"></i>
                                    التقاط صورة
                                </button>
                                <button class="btn btn-warning" id="refreshCamerasBtn">
                                    <i class="fas fa-refresh me-1"></i>
                                    تحديث الكاميرات
                                </button>
                            </div>
                        </div>

                        <!-- Video Display -->
                        <div class="video-container">
                            <img id="videoFeed" src="" alt="عرض الكاميرا" class="img-fluid rounded" style="display: none; width: 100%; max-height: 500px; object-fit: contain;">
                            <div id="videoPlaceholder" class="text-center p-5 bg-light rounded">
                                <i class="fas fa-video-slash fa-3x text-muted mb-3"></i>
                                <p class="text-muted">اضغط على "تشغيل الكاميرا" لبدء العرض المباشر</p>
                            </div>
                        </div>

                        <!-- Recognition Status -->
                        <div class="mt-3">
                            <div class="alert alert-info" id="recognitionStatus" style="display: none;">
                                <i class="fas fa-info-circle me-2"></i>
                                <span id="statusText">جاهز للتعرف على الوجوه</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Add New Face -->
                <div class="card shadow-sm mb-4" id="faces-section">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-user-plus me-2"></i>
                            إضافة وجه جديد
                        </h6>
                    </div>
                    <div class="card-body">
                        <form id="addFaceForm" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="personName" class="form-label">اسم الشخص:</label>
                                <input type="text" class="form-control" id="personName" required>
                            </div>
                            <div class="mb-3">
                                <label for="faceImage" class="form-label">صورة الوجه:</label>
                                <input type="file" class="form-control" id="faceImage" accept="image/*" required>
                                <div class="form-text">يُفضل صورة واضحة تحتوي على وجه واحد فقط</div>
                            </div>
                            <button type="submit" class="btn btn-success w-100">
                                <i class="fas fa-plus me-1"></i>
                                إضافة الوجه
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Known Faces List -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-users me-2"></i>
                            الوجوه المعروفة
                        </h6>
                        <span class="badge bg-light text-dark" id="facesCount">0</span>
                    </div>
                    <div class="card-body p-0">
                        <div id="knownFacesList" class="list-group list-group-flush">
                            <div class="list-group-item text-center text-muted">
                                <i class="fas fa-user-slash fa-2x mb-2"></i>
                                <p class="mb-0">لا توجد وجوه محفوظة</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Logs Section -->
        <div class="row mt-4" id="logs-section">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-history me-2"></i>
                            سجل التعرف على الوجوه
                        </h5>
                        <button class="btn btn-sm btn-outline-dark" id="refreshLogsBtn">
                            <i class="fas fa-refresh me-1"></i>
                            تحديث
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>الاسم</th>
                                        <th>مستوى الثقة</th>
                                        <th>الوقت</th>
                                        <th>مصدر الكاميرا</th>
                                    </tr>
                                </thead>
                                <tbody id="logsTableBody">
                                    <tr>
                                        <td colspan="4" class="text-center text-muted">لا توجد سجلات متاحة</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Section -->
        <div class="row mt-4" id="settings-section">
            <div class="col-md-6">
                <div class="card shadow-sm">
                    <div class="card-header bg-secondary text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-cog me-2"></i>
                            إعدادات التعرف
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="thresholdSlider" class="form-label">
                                عتبة التعرف: <span id="thresholdValue">0.6</span>
                            </label>
                            <input type="range" class="form-range" id="thresholdSlider" min="0.1" max="1.0" step="0.1" value="0.6">
                            <div class="form-text">قيمة أقل = تعرف أكثر حساسية، قيمة أعلى = تعرف أكثر دقة</div>
                        </div>

                        <div class="mb-3">
                            <label for="cameraResolution" class="form-label">دقة الكاميرا:</label>
                            <select class="form-select" id="cameraResolution">
                                <option value="640x480">640x480 (سريع)</option>
                                <option value="1280x720">1280x720 (متوسط)</option>
                                <option value="1920x1080">1920x1080 (عالي)</option>
                            </select>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="autoSaveLogs" checked>
                            <label class="form-check-label" for="autoSaveLogs">
                                حفظ السجلات تلقائياً
                            </label>
                        </div>

                        <button class="btn btn-primary w-100" id="saveSettingsBtn">
                            <i class="fas fa-save me-1"></i>
                            حفظ الإعدادات
                        </button>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card shadow-sm">
                    <div class="card-header bg-dark text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            إحصائيات النظام
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="bg-primary text-white p-3 rounded">
                                    <i class="fas fa-users fa-2x mb-2"></i>
                                    <h4 id="totalFaces">0</h4>
                                    <small>وجوه محفوظة</small>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="bg-success text-white p-3 rounded">
                                    <i class="fas fa-eye fa-2x mb-2"></i>
                                    <h4 id="totalRecognitions">0</h4>
                                    <small>عمليات تعرف</small>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3">
                            <button class="btn btn-outline-danger w-100" id="clearLogsBtn">
                                <i class="fas fa-trash me-1"></i>
                                مسح جميع السجلات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="notificationToast" class="toast" role="alert">
            <div class="toast-header">
                <i class="fas fa-info-circle text-primary me-2"></i>
                <strong class="me-auto">إشعار</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="toastMessage">
                رسالة الإشعار
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>
