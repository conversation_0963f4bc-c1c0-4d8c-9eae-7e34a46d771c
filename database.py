import sqlite3
import json
from datetime import datetime
import os

class FaceDatabase:
    def __init__(self, db_path='face_recognition.db'):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """إنشاء قاعدة البيانات والجداول"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # جدول الوجوه المعروفة
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS known_faces (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                encoding TEXT NOT NULL,
                image_path TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول سجل التعرف
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS recognition_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                face_name TEXT NOT NULL,
                confidence REAL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                camera_source TEXT
            )
        ''')
        
        # جدول إعدادات النظام
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                setting_name TEXT UNIQUE NOT NULL,
                setting_value TEXT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def add_known_face(self, name, encoding, image_path=None):
        """إضافة وجه معروف جديد"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        encoding_json = json.dumps(encoding.tolist())
        cursor.execute('''
            INSERT INTO known_faces (name, encoding, image_path)
            VALUES (?, ?, ?)
        ''', (name, encoding_json, image_path))
        
        conn.commit()
        face_id = cursor.lastrowid
        conn.close()
        return face_id
    
    def get_known_faces(self):
        """الحصول على جميع الوجوه المعروفة"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT id, name, encoding, image_path FROM known_faces')
        faces = cursor.fetchall()
        conn.close()
        
        result = []
        for face in faces:
            result.append({
                'id': face[0],
                'name': face[1],
                'encoding': json.loads(face[2]),
                'image_path': face[3]
            })
        return result
    
    def delete_known_face(self, face_id):
        """حذف وجه معروف"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # الحصول على مسار الصورة لحذفها
        cursor.execute('SELECT image_path FROM known_faces WHERE id = ?', (face_id,))
        result = cursor.fetchone()
        if result and result[0] and os.path.exists(result[0]):
            os.remove(result[0])
        
        cursor.execute('DELETE FROM known_faces WHERE id = ?', (face_id,))
        conn.commit()
        conn.close()
    
    def log_recognition(self, face_name, confidence, camera_source='default'):
        """تسجيل عملية التعرف"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO recognition_log (face_name, confidence, camera_source)
            VALUES (?, ?, ?)
        ''', (face_name, confidence, camera_source))
        
        conn.commit()
        conn.close()
    
    def get_recognition_logs(self, limit=100):
        """الحصول على سجل التعرف"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT face_name, confidence, timestamp, camera_source
            FROM recognition_log
            ORDER BY timestamp DESC
            LIMIT ?
        ''', (limit,))
        
        logs = cursor.fetchall()
        conn.close()
        
        return [{'name': log[0], 'confidence': log[1], 'timestamp': log[2], 'camera': log[3]} for log in logs]
    
    def get_setting(self, setting_name, default_value=None):
        """الحصول على إعداد"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT setting_value FROM system_settings WHERE setting_name = ?', (setting_name,))
        result = cursor.fetchone()
        conn.close()
        
        return result[0] if result else default_value
    
    def set_setting(self, setting_name, setting_value):
        """تعيين إعداد"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO system_settings (setting_name, setting_value, updated_at)
            VALUES (?, ?, CURRENT_TIMESTAMP)
        ''', (setting_name, setting_value))
        
        conn.commit()
        conn.close()
