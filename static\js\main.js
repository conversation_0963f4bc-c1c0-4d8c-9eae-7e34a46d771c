// Face Recognition System JavaScript

class FaceRecognitionApp {
    constructor() {
        this.isStreamActive = false;
        this.currentCamera = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadCameras();
        this.loadKnownFaces();
        this.loadSettings();
        this.loadLogs();
        this.updateStats();
    }

    bindEvents() {
        // Camera controls
        document.getElementById('startCameraBtn').addEventListener('click', () => this.startCamera());
        document.getElementById('stopCameraBtn').addEventListener('click', () => this.stopCamera());
        document.getElementById('captureBtn').addEventListener('click', () => this.captureFrame());
        document.getElementById('refreshCamerasBtn').addEventListener('click', () => this.loadCameras());

        // Face management
        document.getElementById('addFaceForm').addEventListener('submit', (e) => this.addFace(e));

        // Settings
        document.getElementById('thresholdSlider').addEventListener('input', (e) => this.updateThreshold(e));
        document.getElementById('saveSettingsBtn').addEventListener('click', () => this.saveSettings());

        // Logs
        document.getElementById('refreshLogsBtn').addEventListener('click', () => this.loadLogs());
        document.getElementById('clearLogsBtn').addEventListener('click', () => this.clearLogs());

        // Navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => this.smoothScroll(e));
        });
    }

    async loadCameras() {
        try {
            const response = await fetch('/get_cameras');
            const cameras = await response.json();

            const select = document.getElementById('cameraSelect');
            select.innerHTML = '<option value="">اختر الكاميرا...</option>';

            cameras.forEach(camera => {
                const option = document.createElement('option');
                option.value = camera.id;
                option.textContent = camera.name;
                select.appendChild(option);
            });

            if (cameras.length === 0) {
                select.innerHTML = '<option value="">لم يتم العثور على كاميرات</option>';
            }
        } catch (error) {
            this.showNotification('خطأ في تحميل الكاميرات: ' + error.message, 'error');
        }
    }

    async startCamera() {
        const cameraSelect = document.getElementById('cameraSelect');
        const cameraUrl = document.getElementById('cameraUrl').value.trim();

        let cameraId = parseInt(cameraSelect.value);

        console.log('🎥 محاولة تشغيل الكاميرا:', { cameraId, cameraUrl });

        if (isNaN(cameraId) && !cameraUrl) {
            this.showNotification('يرجى اختيار كاميرا أو إدخال رابط كاميرا IP', 'warning');
            return;
        }

        // إظهار حالة التحميل
        const startBtn = document.getElementById('startCameraBtn');
        const originalText = startBtn.innerHTML;
        startBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري التشغيل...';
        startBtn.disabled = true;

        try {
            const requestData = {
                camera_id: isNaN(cameraId) ? 0 : cameraId,
                camera_url: cameraUrl || null
            };

            console.log('📤 إرسال طلب تشغيل الكاميرا:', requestData);

            const response = await fetch('/start_camera', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(requestData)
            });

            const result = await response.json();
            console.log('📥 استجابة الخادم:', result);

            if (result.success) {
                this.isStreamActive = true;
                this.startVideoStream();
                this.updateCameraControls(true);
                this.showNotification(result.message, 'success');
            } else {
                this.showNotification(result.message, 'error');
                // إعادة تعيين الزر
                startBtn.innerHTML = originalText;
                startBtn.disabled = false;
            }
        } catch (error) {
            console.error('❌ خطأ في تشغيل الكاميرا:', error);
            this.showNotification('خطأ في تشغيل الكاميرا: ' + error.message, 'error');
            // إعادة تعيين الزر
            startBtn.innerHTML = originalText;
            startBtn.disabled = false;
        }
    }

    async stopCamera() {
        try {
            const response = await fetch('/stop_camera', { method: 'POST' });
            const result = await response.json();

            this.isStreamActive = false;
            this.stopVideoStream();
            this.updateCameraControls(false);
            this.showNotification(result.message, 'success');
        } catch (error) {
            this.showNotification('خطأ في إيقاف الكاميرا: ' + error.message, 'error');
        }
    }

    startVideoStream() {
        const videoFeed = document.getElementById('videoFeed');
        const placeholder = document.getElementById('videoPlaceholder');

        console.log('📺 بدء تدفق الفيديو...');

        // إضافة معالج للأخطاء
        videoFeed.onerror = (e) => {
            console.error('❌ خطأ في تدفق الفيديو:', e);
            this.showNotification('خطأ في تدفق الفيديو', 'error');
            this.stopVideoStream();
        };

        // إضافة معالج للتحميل
        videoFeed.onload = () => {
            console.log('✅ تم تحميل تدفق الفيديو بنجاح');
        };

        // بدء التدفق
        videoFeed.src = '/video_feed?' + new Date().getTime();
        videoFeed.style.display = 'block';
        placeholder.style.display = 'none';

        // Show recognition status
        const status = document.getElementById('recognitionStatus');
        status.style.display = 'block';
        status.className = 'alert alert-success';
        document.getElementById('statusText').textContent = 'جاري التعرف على الوجوه...';

        // فحص دوري للتأكد من عمل التدفق
        setTimeout(() => {
            if (videoFeed.naturalWidth === 0) {
                console.warn('⚠️ لم يتم تحميل الفيديو بعد 3 ثوان');
                this.showNotification('قد تكون هناك مشكلة في تدفق الفيديو', 'warning');
            }
        }, 3000);
    }

    stopVideoStream() {
        const videoFeed = document.getElementById('videoFeed');
        const placeholder = document.getElementById('videoPlaceholder');

        videoFeed.src = '';
        videoFeed.style.display = 'none';
        placeholder.style.display = 'block';

        // Hide recognition status
        document.getElementById('recognitionStatus').style.display = 'none';
    }

    updateCameraControls(isActive) {
        document.getElementById('startCameraBtn').disabled = isActive;
        document.getElementById('stopCameraBtn').disabled = !isActive;
        document.getElementById('captureBtn').disabled = !isActive;
    }

    async captureFrame() {
        try {
            const response = await fetch('/capture_frame', { method: 'POST' });
            const result = await response.json();

            if (result.success) {
                // يمكن إضافة منطق لحفظ الصورة أو عرضها
                this.showNotification('تم التقاط الإطار بنجاح', 'success');
            } else {
                this.showNotification(result.message, 'error');
            }
        } catch (error) {
            this.showNotification('خطأ في التقاط الإطار: ' + error.message, 'error');
        }
    }

    async addFace(event) {
        event.preventDefault();

        const formData = new FormData();
        const nameInput = document.getElementById('personName');
        const imageInput = document.getElementById('faceImage');

        if (!nameInput.value.trim()) {
            this.showNotification('يرجى إدخال اسم الشخص', 'warning');
            return;
        }

        if (!imageInput.files[0]) {
            this.showNotification('يرجى اختيار صورة', 'warning');
            return;
        }

        formData.append('person_name', nameInput.value.trim());
        formData.append('file', imageInput.files[0]);

        try {
            const response = await fetch('/upload_face', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification(result.message, 'success');
                nameInput.value = '';
                imageInput.value = '';
                this.loadKnownFaces();
                this.updateStats();
            } else {
                this.showNotification(result.message, 'error');
            }
        } catch (error) {
            this.showNotification('خطأ في إضافة الوجه: ' + error.message, 'error');
        }
    }

    async loadKnownFaces() {
        try {
            const response = await fetch('/get_known_faces');
            const faces = await response.json();

            const container = document.getElementById('knownFacesList');
            const countBadge = document.getElementById('facesCount');

            countBadge.textContent = faces.length;

            if (faces.length === 0) {
                container.innerHTML = `
                    <div class="list-group-item text-center text-muted">
                        <i class="fas fa-user-slash fa-2x mb-2"></i>
                        <p class="mb-0">لا توجد وجوه محفوظة</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = faces.map(face => `
                <div class="list-group-item face-item d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <i class="fas fa-user-circle fa-2x text-primary"></i>
                        </div>
                        <div>
                            <h6 class="mb-0">${face.name}</h6>
                            <small class="text-muted">ID: ${face.id}</small>
                        </div>
                    </div>
                    <button class="btn btn-sm btn-outline-danger" onclick="app.deleteFace(${face.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `).join('');
        } catch (error) {
            this.showNotification('خطأ في تحميل الوجوه: ' + error.message, 'error');
        }
    }

    async deleteFace(faceId) {
        if (!confirm('هل أنت متأكد من حذف هذا الوجه؟')) {
            return;
        }

        try {
            const response = await fetch(`/delete_face/${faceId}`, { method: 'DELETE' });
            const result = await response.json();

            if (result.success) {
                this.showNotification(result.message, 'success');
                this.loadKnownFaces();
                this.updateStats();
            } else {
                this.showNotification(result.message, 'error');
            }
        } catch (error) {
            this.showNotification('خطأ في حذف الوجه: ' + error.message, 'error');
        }
    }

    async loadLogs() {
        try {
            const response = await fetch('/get_recognition_logs?limit=50');
            const logs = await response.json();

            const tbody = document.getElementById('logsTableBody');

            if (logs.length === 0) {
                tbody.innerHTML = '<tr><td colspan="4" class="text-center text-muted">لا توجد سجلات متاحة</td></tr>';
                return;
            }

            tbody.innerHTML = logs.map(log => `
                <tr>
                    <td>
                        <strong>${log.name}</strong>
                        ${log.name !== 'غير معروف' ? '<span class="status-indicator online"></span>' : '<span class="status-indicator offline"></span>'}
                    </td>
                    <td>
                        <span class="badge ${log.confidence > 0.8 ? 'bg-success' : log.confidence > 0.6 ? 'bg-warning' : 'bg-danger'}">
                            ${(log.confidence * 100).toFixed(1)}%
                        </span>
                    </td>
                    <td>${new Date(log.timestamp).toLocaleString('ar-SA')}</td>
                    <td>${log.camera}</td>
                </tr>
            `).join('');
        } catch (error) {
            this.showNotification('خطأ في تحميل السجلات: ' + error.message, 'error');
        }
    }

    async clearLogs() {
        if (!confirm('هل أنت متأكد من مسح جميع السجلات؟')) {
            return;
        }

        try {
            // يمكن إضافة endpoint لمسح السجلات
            this.showNotification('تم مسح السجلات بنجاح', 'success');
            this.loadLogs();
            this.updateStats();
        } catch (error) {
            this.showNotification('خطأ في مسح السجلات: ' + error.message, 'error');
        }
    }

    async loadSettings() {
        try {
            const response = await fetch('/get_settings');
            const settings = await response.json();

            document.getElementById('thresholdSlider').value = settings.recognition_threshold || 0.6;
            document.getElementById('thresholdValue').textContent = settings.recognition_threshold || 0.6;
            document.getElementById('cameraResolution').value = settings.camera_resolution || '640x480';
            document.getElementById('autoSaveLogs').checked = settings.auto_save_logs === 'true';
        } catch (error) {
            this.showNotification('خطأ في تحميل الإعدادات: ' + error.message, 'error');
        }
    }

    async saveSettings() {
        const settings = {
            recognition_threshold: document.getElementById('thresholdSlider').value,
            camera_resolution: document.getElementById('cameraResolution').value,
            auto_save_logs: document.getElementById('autoSaveLogs').checked.toString()
        };

        try {
            const response = await fetch('/save_settings', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(settings)
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification(result.message, 'success');
            } else {
                this.showNotification(result.message, 'error');
            }
        } catch (error) {
            this.showNotification('خطأ في حفظ الإعدادات: ' + error.message, 'error');
        }
    }

    async updateThreshold(event) {
        const value = event.target.value;
        document.getElementById('thresholdValue').textContent = value;

        try {
            await fetch('/set_threshold', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ threshold: parseFloat(value) })
            });
        } catch (error) {
            console.error('خطأ في تحديث العتبة:', error);
        }
    }

    async updateStats() {
        try {
            const [facesResponse, logsResponse] = await Promise.all([
                fetch('/get_known_faces'),
                fetch('/get_recognition_logs?limit=1000')
            ]);

            const faces = await facesResponse.json();
            const logs = await logsResponse.json();

            document.getElementById('totalFaces').textContent = faces.length;
            document.getElementById('totalRecognitions').textContent = logs.length;
        } catch (error) {
            console.error('خطأ في تحديث الإحصائيات:', error);
        }
    }

    showNotification(message, type = 'info') {
        const toast = document.getElementById('notificationToast');
        const toastMessage = document.getElementById('toastMessage');
        const toastHeader = toast.querySelector('.toast-header i');

        toastMessage.textContent = message;

        // تغيير أيقونة ولون الإشعار حسب النوع
        toastHeader.className = `fas me-2 ${
            type === 'success' ? 'fa-check-circle text-success' :
            type === 'error' ? 'fa-exclamation-circle text-danger' :
            type === 'warning' ? 'fa-exclamation-triangle text-warning' :
            'fa-info-circle text-primary'
        }`;

        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
    }

    smoothScroll(event) {
        event.preventDefault();
        const targetId = event.target.getAttribute('href');
        if (targetId && targetId.startsWith('#')) {
            const targetElement = document.querySelector(targetId);
            if (targetElement) {
                targetElement.scrollIntoView({ behavior: 'smooth' });

                // تحديث الرابط النشط
                document.querySelectorAll('.nav-link').forEach(link => link.classList.remove('active'));
                event.target.classList.add('active');
            }
        }
    }
}

// تهيئة التطبيق عند تحميل الصفحة
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new FaceRecognitionApp();

    // تحديث السجلات كل 30 ثانية
    setInterval(() => {
        if (app.isStreamActive) {
            app.loadLogs();
            app.updateStats();
        }
    }, 30000);
});
