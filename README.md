# نظام التعرف على الوجوه 👁️

نظام متقدم للتعرف على الوجوه باستخدام Python و Flask مع واجهة ويب أنيقة ومتجاوبة باستخدام Bootstrap 5.

## ✨ الميزات

### 🎯 الميزات الأساسية
- **التعرف على الوجوه في الوقت الفعلي** من خلال الكاميرا
- **دعم كاميرات متعددة**: مدمجة، USB، وكاميرات IP
- **إضافة وإدارة الوجوه المعروفة** بسهولة
- **واجهة ويب عربية** أنيقة ومتجاوبة
- **سجل مفصل** لجميع عمليات التعرف

### 🔧 الميزات المتقدمة
- **إعدادات قابلة للتخصيص** لدقة التعرف
- **دعم دقة كاميرا متعددة** (480p, 720p, 1080p)
- **إحصائيات مفصلة** للنظام
- **حفظ تلقائي للسجلات**
- **واجهة مستخدم تفاعلية** مع إشعارات فورية

## 🛠️ التقنيات المستخدمة

- **Backend**: Python, Flask, OpenCV, face_recognition
- **Frontend**: HTML5, CSS3, JavaScript (ES6+), Bootstrap 5
- **Database**: SQLite
- **UI/UX**: Font Awesome, Google Fonts (Cairo)

## 📋 المتطلبات

### متطلبات النظام
- Python 3.7 أو أحدث
- كاميرا ويب (مدمجة أو خارجية)
- متصفح ويب حديث

### المكتبات المطلوبة
```
Flask==2.3.3
opencv-python==********
face-recognition==1.3.0
numpy==1.24.3
Pillow==10.0.1
SQLAlchemy==2.0.23
python-dotenv==1.0.0
requests==2.31.0
```

## 🚀 التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone <repository-url>
cd face-recognition-system
```

### 2. إنشاء بيئة افتراضية
```bash
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. تشغيل التطبيق
```bash
python app.py
```

### 5. فتح المتصفح
افتح المتصفح وانتقل إلى: `http://localhost:5000`

## 📖 كيفية الاستخدام

### إضافة وجه جديد
1. انتقل إلى قسم "إضافة وجه جديد"
2. أدخل اسم الشخص
3. اختر صورة واضحة للوجه
4. اضغط "إضافة الوجه"

### تشغيل الكاميرا
1. اختر الكاميرا من القائمة المنسدلة
2. أو أدخل رابط كاميرا IP
3. اضغط "تشغيل الكاميرا"
4. ستبدأ عملية التعرف تلقائياً

### إعدادات التعرف
- **عتبة التعرف**: تحكم في حساسية التعرف
- **دقة الكاميرا**: اختر الدقة المناسبة
- **حفظ السجلات**: تفعيل/إلغاء الحفظ التلقائي

## 📁 هيكل المشروع

```
face-recognition-system/
├── app.py                 # الخادم الرئيسي
├── face_recognition_system.py  # نظام التعرف
├── database.py            # إدارة قاعدة البيانات
├── requirements.txt       # المتطلبات
├── README.md             # هذا الملف
├── templates/
│   └── index.html        # الواجهة الرئيسية
├── static/
│   ├── css/
│   │   └── style.css     # التنسيقات المخصصة
│   └── js/
│       └── main.js       # JavaScript للتفاعل
├── known_faces/          # مجلد الصور المعروفة
└── logs/                 # مجلد السجلات
```

## 🔧 التخصيص

### إضافة كاميرا IP
```javascript
// في واجهة المستخدم
http://*************:8080/video
```

### تغيير إعدادات قاعدة البيانات
```python
# في database.py
db_path = 'custom_database.db'
```

### تخصيص عتبة التعرف
```python
# في face_recognition_system.py
recognition_threshold = 0.6  # قيمة بين 0.1 و 1.0
```

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

**1. خطأ في تثبيت face_recognition**
```bash
# Windows
pip install cmake
pip install dlib
pip install face_recognition
```

**2. الكاميرا لا تعمل**
- تأكد من أن الكاميرا غير مستخدمة من تطبيق آخر
- جرب كاميرا مختلفة أو رقم كاميرا مختلف

**3. بطء في التعرف**
- قلل دقة الكاميرا من الإعدادات
- زد عتبة التعرف لتقليل المعالجة

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## 📞 الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات، يرجى:
- فتح Issue في GitHub
- التواصل عبر البريد الإلكتروني

## 🙏 شكر وتقدير

- مكتبة face_recognition
- Bootstrap 5
- Font Awesome
- OpenCV
- Flask

---

**ملاحظة**: تأكد من احترام خصوصية الأشخاص عند استخدام نظام التعرف على الوجوه.
