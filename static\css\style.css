/* Custom CSS for Face Recognition System */

/* Arabic Font */
body {
    font-family: 'Cairo', sans-serif;
    background-color: #f8f9fa;
}

/* Navigation */
.navbar-brand {
    font-weight: 600;
    font-size: 1.5rem;
}

/* Cards */
.card {
    border: none;
    border-radius: 15px;
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
    font-weight: 600;
}

/* Video Container */
.video-container {
    position: relative;
    background: #000;
    border-radius: 10px;
    overflow: hidden;
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
}

#videoFeed {
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

#videoPlaceholder {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
    border: 2px dashed #adb5bd;
}

/* Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
    border: none;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    border: none;
    color: #212529;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
    border: none;
}

/* Form Controls */
.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

/* Range Slider */
.form-range {
    height: 8px;
}

.form-range::-webkit-slider-thumb {
    background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
    border: none;
    border-radius: 50%;
    width: 20px;
    height: 20px;
}

.form-range::-moz-range-thumb {
    background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
    border: none;
    border-radius: 50%;
    width: 20px;
    height: 20px;
}

/* Known Faces List */
.face-item {
    transition: background-color 0.2s ease;
}

.face-item:hover {
    background-color: #f8f9fa;
}

.face-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #007bff;
}

/* Statistics Cards */
.bg-primary, .bg-success, .bg-info, .bg-warning, .bg-danger {
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-info) 100%) !important;
}

.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}

/* Table */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: background-color 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(0,123,255,0.05);
}

/* Alerts */
.alert {
    border: none;
    border-radius: 10px;
    font-weight: 500;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

/* Toast */
.toast {
    border-radius: 10px;
    border: none;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container-fluid {
        padding: 0 10px;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        margin-bottom: 0.5rem;
    }
    
    .video-container {
        min-height: 200px;
    }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #0056b3 0%, #520dc2 100%);
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Status Indicators */
.status-online {
    color: #28a745;
}

.status-offline {
    color: #dc3545;
}

.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-left: 5px;
}

.status-indicator.online {
    background-color: #28a745;
    animation: pulse 2s infinite;
}

.status-indicator.offline {
    background-color: #dc3545;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
}
