#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الكاميرا - تشخيص مشاكل الكاميرا
"""

import cv2
import sys

def test_camera_basic():
    """اختبار أساسي للكاميرا"""
    print("🔍 اختبار الكاميرا الأساسي...")
    
    try:
        # محاولة فتح الكاميرا بطرق مختلفة
        methods = [
            ("الافتراضي", lambda i: cv2.VideoCapture(i)),
            ("DirectShow", lambda i: cv2.VideoCapture(i, cv2.CAP_DSHOW)),
            ("MSMF", lambda i: cv2.VideoCapture(i, cv2.CAP_MSMF)),
        ]
        
        for method_name, method_func in methods:
            print(f"\n📹 اختبار {method_name}:")
            
            for camera_id in range(3):
                try:
                    cap = method_func(camera_id)
                    
                    if cap.isOpened():
                        ret, frame = cap.read()
                        
                        if ret and frame is not None:
                            height, width = frame.shape[:2]
                            fps = cap.get(cv2.CAP_PROP_FPS)
                            
                            print(f"  ✅ كاميرا {camera_id}: تعمل ({width}x{height}, {fps:.1f}fps)")
                            
                            # عرض الكاميرا لثانيتين
                            cv2.imshow(f'Camera {camera_id} Test', frame)
                            cv2.waitKey(2000)
                            cv2.destroyAllWindows()
                            
                        else:
                            print(f"  ❌ كاميرا {camera_id}: متصلة لكن لا تقرأ إطارات")
                    else:
                        print(f"  ❌ كاميرا {camera_id}: فشل في الفتح")
                    
                    cap.release()
                    
                except Exception as e:
                    print(f"  ❌ كاميرا {camera_id}: خطأ - {e}")
    
    except Exception as e:
        print(f"❌ خطأ عام في اختبار الكاميرا: {e}")

def test_camera_advanced():
    """اختبار متقدم للكاميرا"""
    print("\n🔬 اختبار متقدم للكاميرا...")
    
    try:
        cap = cv2.VideoCapture(0, cv2.CAP_DSHOW)
        
        if not cap.isOpened():
            print("❌ فشل في فتح الكاميرا 0")
            return
        
        # الحصول على خصائص الكاميرا
        properties = {
            'العرض': cv2.CAP_PROP_FRAME_WIDTH,
            'الارتفاع': cv2.CAP_PROP_FRAME_HEIGHT,
            'FPS': cv2.CAP_PROP_FPS,
            'السطوع': cv2.CAP_PROP_BRIGHTNESS,
            'التباين': cv2.CAP_PROP_CONTRAST,
            'التشبع': cv2.CAP_PROP_SATURATION,
        }
        
        print("📊 خصائص الكاميرا:")
        for name, prop in properties.items():
            value = cap.get(prop)
            print(f"  {name}: {value}")
        
        # اختبار تغيير الدقة
        print("\n🔧 اختبار تغيير الدقة:")
        resolutions = [(640, 480), (1280, 720), (1920, 1080)]
        
        for width, height in resolutions:
            cap.set(cv2.CAP_PROP_FRAME_WIDTH, width)
            cap.set(cv2.CAP_PROP_FRAME_HEIGHT, height)
            
            actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            ret, frame = cap.read()
            if ret:
                print(f"  ✅ {width}x{height} -> {actual_width}x{actual_height}")
            else:
                print(f"  ❌ {width}x{height} -> فشل في القراءة")
        
        cap.release()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار المتقدم: {e}")

def test_face_detection():
    """اختبار كشف الوجوه"""
    print("\n👤 اختبار كشف الوجوه...")
    
    try:
        # تحميل مصنف الوجوه
        face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        
        if face_cascade.empty():
            print("❌ فشل في تحميل مصنف الوجوه")
            return
        
        cap = cv2.VideoCapture(0, cv2.CAP_DSHOW)
        
        if not cap.isOpened():
            print("❌ فشل في فتح الكاميرا")
            return
        
        print("✅ بدء كشف الوجوه... (اضغط 'q' للخروج)")
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # تحويل إلى رمادي
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # كشف الوجوه
            faces = face_cascade.detectMultiScale(gray, 1.1, 4)
            
            # رسم مربعات حول الوجوه
            for (x, y, w, h) in faces:
                cv2.rectangle(frame, (x, y), (x+w, y+h), (255, 0, 0), 2)
                cv2.putText(frame, f'Face {len(faces)}', (x, y-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.9, (255, 0, 0), 2)
            
            # عرض النتيجة
            cv2.imshow('Face Detection Test', frame)
            
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
        
        cap.release()
        cv2.destroyAllWindows()
        print("✅ انتهى اختبار كشف الوجوه")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار كشف الوجوه: {e}")

def main():
    """الدالة الرئيسية"""
    print("🎯 برنامج تشخيص الكاميرا")
    print("=" * 40)
    
    # اختبار OpenCV
    print(f"📦 إصدار OpenCV: {cv2.__version__}")
    
    # الاختبارات
    test_camera_basic()
    
    # سؤال المستخدم عن الاختبارات المتقدمة
    try:
        choice = input("\n❓ هل تريد تشغيل الاختبار المتقدم؟ (y/n): ").lower()
        if choice == 'y':
            test_camera_advanced()
        
        choice = input("\n❓ هل تريد اختبار كشف الوجوه؟ (y/n): ").lower()
        if choice == 'y':
            test_face_detection()
            
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار")
    
    print("\n✅ انتهى التشخيص")

if __name__ == "__main__":
    main()
