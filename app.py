from flask import Flask, render_template, request, jsonify, Response
import cv2
import os
from werkzeug.utils import secure_filename
from face_recognition_system import FaceRecognitionSystem
from database import FaceDatabase
import base64

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'

# إعدادات التطبيق
UPLOAD_FOLDER = 'known_faces'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}

# إنشاء المجلدات المطلوبة
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs('logs', exist_ok=True)

# تهيئة النظام
face_system = FaceRecognitionSystem()
db = FaceDatabase()

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return render_template('index.html')

@app.route('/upload_face', methods=['POST'])
def upload_face():
    """رفع صورة وجه جديد"""
    if 'file' not in request.files:
        return jsonify({'success': False, 'message': 'لم يتم اختيار ملف'})

    file = request.files['file']
    person_name = request.form.get('person_name', '').strip()

    if file.filename == '':
        return jsonify({'success': False, 'message': 'لم يتم اختيار ملف'})

    if not person_name:
        return jsonify({'success': False, 'message': 'يرجى إدخال اسم الشخص'})

    if file and allowed_file(file.filename):
        filename = secure_filename(f"{person_name}_{file.filename}")
        filepath = os.path.join(UPLOAD_FOLDER, filename)
        file.save(filepath)

        # إضافة الوجه إلى النظام
        success, message = face_system.add_face_from_image(filepath, person_name)

        if success:
            return jsonify({'success': True, 'message': message})
        else:
            # حذف الملف في حالة الفشل
            if os.path.exists(filepath):
                os.remove(filepath)
            return jsonify({'success': False, 'message': message})

    return jsonify({'success': False, 'message': 'نوع الملف غير مدعوم'})

@app.route('/get_known_faces')
def get_known_faces():
    """الحصول على قائمة الوجوه المعروفة"""
    faces = db.get_known_faces()
    return jsonify(faces)

@app.route('/delete_face/<int:face_id>', methods=['DELETE'])
def delete_face(face_id):
    """حذف وجه معروف"""
    try:
        db.delete_known_face(face_id)
        face_system.load_known_faces()  # إعادة تحميل الوجوه
        return jsonify({'success': True, 'message': 'تم حذف الوجه بنجاح'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في حذف الوجه: {str(e)}'})

@app.route('/get_cameras')
def get_cameras():
    """الحصول على قائمة الكاميرات المتاحة"""
    cameras = face_system.get_available_cameras()
    return jsonify(cameras)

@app.route('/start_camera', methods=['POST'])
def start_camera():
    """بدء تشغيل الكاميرا"""
    try:
        data = request.get_json()
        camera_id = data.get('camera_id', 0)
        camera_url = data.get('camera_url')

        print(f"🎥 محاولة تشغيل الكاميرا - ID: {camera_id}, URL: {camera_url}")

        success, message = face_system.start_camera(camera_id, camera_url)

        print(f"📊 نتيجة تشغيل الكاميرا - نجح: {success}, رسالة: {message}")

        return jsonify({'success': success, 'message': message})
    except Exception as e:
        error_msg = f"خطأ في تشغيل الكاميرا: {str(e)}"
        print(f"❌ {error_msg}")
        return jsonify({'success': False, 'message': error_msg})

@app.route('/stop_camera', methods=['POST'])
def stop_camera():
    """إيقاف الكاميرا"""
    face_system.stop_camera()
    return jsonify({'success': True, 'message': 'تم إيقاف الكاميرا'})

@app.route('/video_feed')
def video_feed():
    """تدفق الفيديو المباشر"""
    def generate():
        print("📹 بدء تدفق الفيديو...")
        frame_count = 0

        while True:
            try:
                frame = face_system.get_frame()
                if frame is None:
                    print("⚠️ لم يتم الحصول على إطار من الكاميرا")
                    break

                # تحويل الإطار إلى JPEG
                ret, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 80])
                if not ret:
                    print("❌ فشل في تحويل الإطار إلى JPEG")
                    continue

                frame_count += 1
                if frame_count % 30 == 0:  # طباعة كل 30 إطار
                    print(f"📊 تم إرسال {frame_count} إطار")

                frame_bytes = buffer.tobytes()
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')

            except Exception as e:
                print(f"❌ خطأ في تدفق الفيديو: {e}")
                break

        print("🛑 انتهى تدفق الفيديو")

    return Response(generate(), mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/capture_frame', methods=['POST'])
def capture_frame():
    """التقاط إطار واحد"""
    frame = face_system.get_frame()
    if frame is None:
        return jsonify({'success': False, 'message': 'فشل في التقاط الإطار'})

    # تحويل الإطار إلى base64
    ret, buffer = cv2.imencode('.jpg', frame)
    if ret:
        frame_base64 = base64.b64encode(buffer).decode('utf-8')
        return jsonify({'success': True, 'image': frame_base64})

    return jsonify({'success': False, 'message': 'فشل في معالجة الإطار'})

@app.route('/get_recognition_logs')
def get_recognition_logs():
    """الحصول على سجل التعرف"""
    limit = request.args.get('limit', 50, type=int)
    logs = db.get_recognition_logs(limit)
    return jsonify(logs)

@app.route('/set_threshold', methods=['POST'])
def set_threshold():
    """تعيين عتبة التعرف"""
    data = request.get_json()
    threshold = data.get('threshold', 0.6)

    try:
        face_system.set_recognition_threshold(float(threshold))
        return jsonify({'success': True, 'message': 'تم تحديث عتبة التعرف'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ: {str(e)}'})

@app.route('/get_settings')
def get_settings():
    """الحصول على إعدادات النظام"""
    settings = {
        'recognition_threshold': db.get_setting('recognition_threshold', '0.6'),
        'camera_resolution': db.get_setting('camera_resolution', '640x480'),
        'auto_save_logs': db.get_setting('auto_save_logs', 'true')
    }
    return jsonify(settings)

@app.route('/save_settings', methods=['POST'])
def save_settings():
    """حفظ إعدادات النظام"""
    data = request.get_json()

    for key, value in data.items():
        db.set_setting(key, str(value))

    return jsonify({'success': True, 'message': 'تم حفظ الإعدادات'})

if __name__ == '__main__':
    import sys
    print("🚀 بدء تشغيل نظام التعرف على الوجوه...", flush=True)
    print("📍 الخادم متاح على: http://localhost:5000", flush=True)
    print("📍 أو: http://127.0.0.1:5000", flush=True)
    print("⚠️  ملاحظة: إذا لم تكن مكتبة face_recognition مثبتة، سيعمل النظام في وضع كشف الوجوه فقط", flush=True)
    print("-" * 50, flush=True)

    # تأكد من طباعة الرسائل فوراً
    sys.stdout.flush()

    try:
        app.run(debug=True, host='0.0.0.0', port=5000, use_reloader=False)
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}", flush=True)
        input("اضغط Enter للخروج...")
